const { <PERSON><PERSON><PERSON>, <PERSON>rowser, <PERSON>, <PERSON>, until } = require("selenium-webdriver");
const { getCurrentDate, awaitTime } = require("../../utils/Common");
//发货
module.exports = function waybillEdit(driver, order_id) {
  return new Promise(async (resolve, reject) => {
    //点击运单
    // await awaitTime(3)
    await driver
      .findElement(
        By.xpath("/html/body/div/div[1]/div[2]/div[1]/div[1]/div/ul/div[5]")
      )
      .click();
    await new Promise((res) => setTimeout(res, 500));

    // 运单
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[1]/div[1]/div/ul/div[5]/li/ul/a[1]/li"
        )
      )
      .click();
    await new Promise((res) => setTimeout(res, 1500));

    //根据订单号搜索
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[2]/section/div/div[1]/div[2]/form[1]/div[4]/div/div/input"
        )
      )
      .sendKeys(order_id);
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[2]/section/div/div[1]/div[2]/form[1]/div[39]/div/button[1]"
        )
      )
      .click();
    await new Promise((res) => setTimeout(res, 1000));
    //获取运单号
    let transport_id = await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[2]/section/div/div[2]/div[3]/div[1]/div[3]/table/tbody/tr/td[4]/div"
        )
      )
      .getText();
    resolve(transport_id);
  });
};
