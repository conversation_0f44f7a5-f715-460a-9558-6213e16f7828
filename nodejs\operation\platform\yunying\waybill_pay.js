const { By } = require("selenium-webdriver");
const { getCurrentDate, awaitTime } = require("../../utils/Common");
const { until } = require("selenium-webdriver");
//发货
module.exports = function waybill_pay(driver, transport_id) {
  return new Promise(async (resolve, reject) => {
    await driver
      .findElement(By.xpath("/html/body/div/section/section/aside/ul/li[6]"))
      .click();
    await new Promise((res) => setTimeout(res, 500));
    await driver
      .findElement(
        By.xpath("/html/body/div/section/section/aside/ul/li[6]/ul/li[2]")
      )
      .click();
    await new Promise((res) => setTimeout(res, 1000));

    //根据运单号查询代付款
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/section/section/div/main/div/div/div[2]/form/div[2]/div/div/input"
        )
      )
      .sendKeys(transport_id);
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/section/section/div/main/div/div/div[2]/form/div[14]/div/button[1]"
        )
      )
      .click();
    await new Promise((res) => setTimeout(res, 1500));

    //勾选全部代付款
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/section/section/div/main/div/div/div[4]/div/div[2]/table/thead/tr/th[1]/div/label/span"
        )
      )
      .click();
    await driver
      .findElement(
        By.xpath(
          "/html/body/div[1]/section/section/div/main/div/div/div[3]/div[2]/button"
        )
      )
      .click();

    //等待弹出框显示->确认
    await driver.wait(
      until.elementLocated(By.xpath("//*[@aria-label='dialog']")),
      5000
    );
    await driver
      .findElement(By.xpath("/html/body/div[2]/div/div[3]/button[2]"))
      .click();
    await new Promise((res) => setTimeout(res, 1500));

    //等待输入支付密码弹窗显示
    await driver.wait(
      until.elementLocated(By.xpath("//*[@aria-label='请输入支付密码']")),
      5000
    );
    //输入密码
    await driver
      .findElement(By.xpath("//*[@autocomplete='new-password']"))
      .sendKeys("123456");
    //点击确定
    await driver
      .findElement(
        By.xpath("//*[@aria-label='请输入支付密码']/div[3]/span/button[2]")
      )
      .click();
    await new Promise((res) => setTimeout(res, 1500));
    resolve();
  });
};
