const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, until } = require("selenium-webdriver");
const assert = require("node:assert");
const { awaitTime } = require("../utils/Common.js");

// 鸿飞达运营端 模块
const Login = require("../platform/yunying/login.js");
const releaseSource = require("../platform/yunying/releaseSource.js");
const orderList = require("../platform/yunying/order.js");
const acceptAbleBill = require("../platform/yunying/acceptAbleBill.js");
const wayBillPay = require("../platform/yunying/waybill_pay.js");

// 鸿飞达管理后台 模块
const backLogin = require("../platform/wuliu/backLogin.js");
const wayBillEdit = require("../platform/wuliu/waybill_edit.js");
const wayBillReview = require("../platform/wuliu/waybill_review.js");
(async function main() {
  const driver = new Builder().forBrowser(Browser.CHROME).build();
  const ownerWindow = await driver.getWindowHandle();
  await driver.manage().window().maximize();
  // 登录验证
  await Login(driver);
  //-------------------------------发货开始---------------------------
  // 发货成功，返回订单昵称 order_name
  let { order_name } = await releaseSource(driver);
  //-------------------------------发货结束---------------------------

  //---------------------------获取订单编号开始---------------------
  let { order_id } = await orderList(driver, order_name);
  console.log("订单ID:" + order_id);
  //---------------------------获取订单编号结束---------------------

  //  ----- 需在司机端进行接单操作， 等待时间20秒 ---------------
  console.log("请在司机端进行接单操作， 等待时间5分钟");
  await awaitTime(300);
  //  ----- 司机端操作结束

  //-------------------------------运单收货开始--------------------------
  // 收获审核 - 可收货运单
  await acceptAbleBill(driver, order_id);
  //-------------------------------运单收货结束--------------------------

  // ----------------------------切换到管理后台开始-------------------
  //判断当前窗口数量是否等于1
  assert((await driver.getAllWindowHandles()).length === 1);
  //打开新的标签页
  await driver.switchTo().newWindow("tab"); //标签页
  //等待新窗口
  await driver.wait(
    async () => (await driver.getAllWindowHandles()).length === 2,
    10000
  );
  let windows = await driver.getAllWindowHandles();
  const targetAdminWindow = windows.find((handle) => handle !== ownerWindow);
  if (targetAdminWindow) {
    await driver.switchTo().window(targetAdminWindow);
  }
  //------------------------------切换到管理后台结束-------------------

  //--------------------登录管理后台开始-------------------------------
  const adminWindow = await driver.getWindowHandle();
  await backLogin(driver);
  //--------------------登录管理后台结束-------------------------------
  // 运单编辑
  let { transport_id } = await wayBillEdit(driver, order_id);
  console.log("运单ID:" + transport_id);
  // 运单审核
  await wayBillReview(driver, order_id, ownerWindow, adminWindow);
  //切换到运营端窗口
  windows = await driver.getAllWindowHandles();
  const targetOwnWindow = windows.find((handle) => handle !== adminWindow);
  if (targetOwnWindow) {
    await driver.switchTo().window(targetOwnWindow);
  }
  // 运单付款
  await wayBillPay(driver, transport_id);
  console.log("测试通过");
})();
