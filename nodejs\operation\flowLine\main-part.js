const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, until } = require("selenium-webdriver");
const assert = require("node:assert");

const Login = require("../platform/yunying/login.js");
const { awaitTime } = require("../utils/Common.js");

const wayBillPay = require("../platform/yunying/waybill_pay.js");

// 鸿飞达管理后台 模块
const backLogin = require("../platform/wuliu/backLogin.js");
const wayBillEdit = require("../platform/wuliu/waybill_edit.js");
const wayBillReview = require("../platform/wuliu/waybill_review.js");

(async function main() {
  const driver = new Builder().forBrowser(Browser.CHROME).build();
  const ownerWindow = await driver.getWindowHandle();
  await driver.manage().window().maximize();
  // 登录验证
  await <PERSON><PERSON>(driver);
  // ----------------------------切换到管理后台开始-------------------
  //判断当前窗口数量是否等于1
  assert((await driver.getAllWindowHandles()).length === 1);
  //打开新的标签页
  await driver.switchTo().newWindow("tab"); //标签页
  //等待新窗口
  await driver.wait(
    async () => (await driver.getAllWindowHandles()).length === 2,
    10000
  );
  let windows = await driver.getAllWindowHandles();
  const targetAdminWindow = windows.find((handle) => handle !== ownerWindow);
  if (targetAdminWindow) {
    await driver.switchTo().window(targetAdminWindow);
  }
  //------------------------------切换到管理后台结束-------------------

  //--------------------登录管理后台开始-------------------------------
  const adminWindow = await driver.getWindowHandle();
  await backLogin(driver);
  //--------------------登录管理后台结束-------------------------------
  let order_id = "D2506231355110001";
  let transport_id = "Y2506231355490001";
  // 运单编辑
  //let { transport_id } = await wayBillEdit(driver, order_id);
  // 运单审核
  //await wayBillReview(driver, order_id, ownerWindow, adminWindow);
  //切换到运营端窗口
  windows = await driver.getAllWindowHandles();
  const targetOwnWindow = windows.find((handle) => handle !== adminWindow);
  if (targetOwnWindow) {
    await driver.switchTo().window(targetOwnWindow);
  }
  // 运单付款
  await wayBillPay(driver, transport_id);
  console.log("测试通过");
})();
