const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, until } = require("selenium-webdriver");
const { getCurrentDate, awaitTime } = require("../../utils/Common");
//发货
module.exports = function waybillReview(
  driver,
  order_id,
  ownerWindow,
  adminWindow
) {
  return new Promise(async (resolve, reject) => {
    //运单审核
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[1]/div[1]/div/ul/div[5]/li/ul/a[2]/li"
        )
      )
      .click();
    await new Promise((res) => setTimeout(res, 1000));

    //根据订单号搜索
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[2]/section/div/form/div[1]/div/div/input"
        )
      )
      .sendKeys(order_id);
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[2]/section/div/form/div[10]/div/button[1]"
        )
      )
      .click();
    await new Promise((res) => setTimeout(res, 1000));
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[2]/section/div/div/div[2]/div[4]/div[2]/table/tbody/tr/td[15]/div/button"
        )
      )
      .click();

    //等待打开审核页
    await new Promise((res) => setTimeout(res, 2000));
    windows = await driver.getAllWindowHandles();
    const targetReviewWindow = windows.find(
      (handle) => handle !== ownerWindow && handle !== adminWindow
    );
    if (targetReviewWindow) {
      await driver.switchTo().window(targetReviewWindow);
    }
    await new Promise((res) => setTimeout(res, 1000));
    //点击通过审核
    await driver
      .findElement(
        By.xpath(
          "/html/body/div/div[1]/div[2]/div[2]/section/div/div[2]/div[2]/button[1]"
        )
      )
      .click();
    await new Promise((res) => setTimeout(res, 1000));

    //获取提示框
    await driver
      .findElement(By.xpath("/html/body/div[2]/div/div[3]/button[2]"))
      .click();
    await new Promise((res) => setTimeout(res, 5000));
    resolve();
  });
};
